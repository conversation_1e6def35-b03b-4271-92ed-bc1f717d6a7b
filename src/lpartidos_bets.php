<?php 

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
	session_start();
}

global $conexion;

use App\classes\PartidoBetDetalle;
use App\classes\PartidoBet;

require_once '../config/config.php';
require_once __ROOT__ . '/src/query/conexion.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/classes/usuario.php';
require_once __ROOT__ . '/src/general/preparar.php';

#region get
if ($_SERVER['REQUEST_METHOD'] == 'GET') {
    try {
        // Handle any GET parameters if needed
        if (isset($_GET['m'])) {
            $success_display = 'show';
            $success_text = 'Operación completada exitosamente.';
        }
    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion get

#region post
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        // Check if it's an AJAX request
        if (isset($_POST['ajax_check']) && $_POST['ajax_check'] == 1) {

            if (isset($_POST['action']) && $_POST['action'] == 'crear_partido_bet') {

                // Get and validate input data
                $valorApostado = $_POST['valor_apostado'] ?? '';
                $cuota = $_POST['cuota'] ?? '';
                $selectedBetDetails = $_POST['selected_bet_details'] ?? [];

                // Validate valor apostado
                validar_textovacio($valorApostado, 'Debe especificar el valor apostado');
                // Remove dots (thousands separators in Colombian peso format) before cleaning
                $valorApostadoSinPuntos = str_replace('.', '', $valorApostado);
                $valorApostadoLimpio = format_numberclean($valorApostadoSinPuntos);
                if (!is_numeric($valorApostadoLimpio) || (float)$valorApostadoLimpio <= 0) {
                    throw new Exception('El valor apostado debe ser un número válido mayor que cero');
                }

                // Validate cuota
                validar_textovacio($cuota, 'Debe especificar la cuota');
                if (!is_numeric($cuota) || (float)$cuota <= 1) {
                    throw new Exception('La cuota debe ser un número válido mayor que 1.00');
                }

                // Validate selected bet details
                validar_array($selectedBetDetails, 'Debe seleccionar al menos una apuesta de la tabla');

                // Set timezone to Bogotá
                date_default_timezone_set('America/Bogota');

                // Create new PartidoBet
                $partidoBet = new PartidoBet();
                $partidoBet->setValorApostado((float)$valorApostadoLimpio);
                $partidoBet->setCuota((float)$cuota);
                $partidoBet->setFechaCreado(date('Y-m-d H:i:s'));
                $partidoBet->setEstado(1);
                $partidoBet->setEsCerrado(0);
                $partidoBet->setEsGanado(0);
                $partidoBet->setValorRecibido(0.0);
                $partidoBet->setValorProfit(0.0);

                // Save the PartidoBet
                if (!$partidoBet->guardar($conexion)) {
                    throw new Exception('Error al guardar la apuesta principal');
                }

                // Get the new PartidoBet ID
                $newPartidoBetId = $partidoBet->getId();
                if (!$newPartidoBetId) {
                    throw new Exception('Error al obtener el ID de la nueva apuesta');
                }

                // Update selected PartidoBetDetalle records
                $updatedCount = 0;
                foreach ($selectedBetDetails as $betDetailId) {
                    try {
                        // Load the existing PartidoBetDetalle
                        $partidoBetDetalle = PartidoBetDetalle::get($betDetailId, $conexion);
                        if ($partidoBetDetalle) {
                            // Update the id_partido_bet
                            $partidoBetDetalle->setIdPartidoBet($newPartidoBetId);

                            // Save the updated record
                            if ($partidoBetDetalle->guardar($conexion)) {
                                $updatedCount++;
                            }
                        }
                    } catch (Exception $e) {
                        // Log the error but continue with other records
                        error_log("Error updating bet detail ID $betDetailId: " . $e->getMessage());
                    }
                }

                if ($updatedCount == 0) {
                    throw new Exception('No se pudieron asociar las apuestas seleccionadas');
                }

                // Return success response
                echo json_encode([
                    'status' => 'success',
                    'message' => 'Apuesta creada exitosamente',
                    'data' => [
                        'partido_bet_id' => $newPartidoBetId,
                        'valor_apostado' => $valorApostadoLimpio,
                        'cuota' => $cuota,
                        'updated_count' => $updatedCount
                    ]
                ]);
                exit;
            }

            if (isset($_POST['action']) && $_POST['action'] == 'deactivate_bet_detail') {

                // Get and validate input data
                $betDetailId = $_POST['bet_detail_id'] ?? '';

                // Validate bet detail ID
                validar_textovacio($betDetailId, 'Debe especificar el ID de la apuesta a desactivar');

                // Deactivate the PartidoBetDetalle record
                if (!PartidoBetDetalle::deactivate($betDetailId, $conexion)) {
                    throw new Exception('Error al desactivar la apuesta');
                }

                // Return success response
                echo json_encode([
                    'status' => 'success',
                    'message' => 'Apuesta desactivada exitosamente'
                ]);
                exit;
            }
        }

    } catch (Exception $e) {
        // Return error response
        echo json_encode([
            'status' => 'error',
            'message' => $e->getMessage()
        ]);
        exit;
    }
}
#endregion post

#region try
try {
    $partidosBets = PartidoBetDetalle::getListForBetsListing($conexion);

} catch (Exception $e) {
    $error_display = 'show';
    $error_text = $e->getMessage();
}
#endregion try

require_once __ROOT__ . '/views/lpartidos_bets.view.php';

?>
